#!/usr/bin/env python3
"""
Script to check and display the contents of the Excel log file.
"""

import os
import glob
from openpyxl import load_workbook

def check_latest_excel_log():
    """Check the latest Excel log file and display its contents."""
    # Find the latest Excel log file
    log_files = glob.glob("File_Processing_Log_*.xlsx")
    
    if not log_files:
        print("❌ No Excel log files found")
        return
    
    # Get the most recent log file
    latest_log = max(log_files, key=os.path.getctime)
    print(f"📄 Checking Excel log: {latest_log}")
    print("=" * 80)
    
    try:
        # Load the workbook
        workbook = load_workbook(latest_log, read_only=True)
        worksheet = workbook.active
        
        # Get headers
        headers = []
        for cell in worksheet[1]:
            headers.append(cell.value if cell.value else "")
        
        print("📋 Column Headers:")
        for i, header in enumerate(headers, 1):
            print(f"  {i}. {header}")
        
        print(f"\n📊 Data Rows:")
        print("-" * 80)
        
        # Display data rows
        row_count = 0
        for row in worksheet.iter_rows(min_row=2, values_only=True):
            if any(cell for cell in row):  # Skip empty rows
                row_count += 1
                print(f"\nRow {row_count}:")
                for i, (header, value) in enumerate(zip(headers, row)):
                    if header and value:
                        print(f"  {header}: {value}")
        
        print(f"\n📈 Summary:")
        print(f"  Total rows processed: {row_count}")
        
        # Count by error status
        error_statuses = {}
        artifacts_types = {}
        
        for row in worksheet.iter_rows(min_row=2, values_only=True):
            if any(cell for cell in row):
                # Error Status (column 6)
                error_status = row[5] if len(row) > 5 else ""
                if error_status:
                    error_statuses[error_status] = error_statuses.get(error_status, 0) + 1
                
                # Artifacts Type (column 7)
                artifacts_type = row[6] if len(row) > 6 else ""
                if artifacts_type:
                    artifacts_types[artifacts_type] = artifacts_types.get(artifacts_type, 0) + 1
        
        print(f"\n📊 Error Status Breakdown:")
        for status, count in error_statuses.items():
            print(f"  {status}: {count}")
        
        print(f"\n🏷️  Artifacts Type Breakdown:")
        for artifact_type, count in artifacts_types.items():
            print(f"  {artifact_type}: {count}")
        
        workbook.close()
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")

if __name__ == "__main__":
    check_latest_excel_log()
