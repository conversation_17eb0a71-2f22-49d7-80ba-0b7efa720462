#!/usr/bin/env python3
"""
Enhanced <PERSON><PERSON><PERSON> to copy files based on paths in a CSV file to an Output folder.

This script:
1. Reads a CSV file with a "True Path" column
2. Creates folder structure based on paths in the CSV
3. Copies files from source directory to Output directory maintaining original structure
4. Generates a comprehensive CSV log with detailed processing information
5. Provides statistics and summary information

Features:
- Supports command line arguments for source directory and CSV file
- Creates detailed processing logs similar to File_Processing_Log format
- Handles missing files gracefully with detailed error reporting
- Maintains original folder structure in output directory
- Provides comprehensive statistics and progress reporting
"""

import os
import csv
import shutil
import argparse
import datetime
import sys
import hashlib
import time
from pathlib import Path
from typing import List, Dict, Tuple, Optional

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Enhanced script to copy files listed in a CSV to an Output folder with original structure",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Copy files from source directory based on CSV file paths
  python copy_files_from_csv.py -s F:\\Project_Stargazing\\Encase\\Existing_actual_files -c Project_stargazing_encase_final_filtered.csv

  # Specify custom output directory
  python copy_files_from_csv.py -s F:\\Project_Stargazing\\Encase\\Existing_actual_files -c file_list.csv -o CustomOutput

  # Enable verbose output
  python copy_files_from_csv.py -s F:\\Project_Stargazing\\Encase\\Existing_actual_files -c file_list.csv -v
        """
    )

    parser.add_argument('-s', '--source', required=True,
                       help='Source directory containing the files to copy')
    parser.add_argument('-c', '--csv', required=True,
                       help='Path to the CSV file with "True Path" column')
    parser.add_argument('-o', '--output',
                       help='Output directory (default: "Output" in current directory)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose output for detailed progress information')

    return parser.parse_args()

def get_file_info(file_path: str) -> Dict:
    """
    Get detailed information about a file.

    Args:
        file_path: Path to the file

    Returns:
        Dictionary containing file information
    """
    info = {
        "exists": False,
        "size": 0,
        "size_formatted": "0 B",
        "modified_time": "",
        "created_time": "",
        "is_directory": False,
        "extension": "",
        "md5_hash": ""
    }

    try:
        if os.path.exists(file_path):
            info["exists"] = True
            stat = os.stat(file_path)
            info["size"] = stat.st_size
            info["size_formatted"] = format_file_size(stat.st_size)
            info["modified_time"] = datetime.datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            info["created_time"] = datetime.datetime.fromtimestamp(stat.st_ctime).strftime("%Y-%m-%d %H:%M:%S")
            info["is_directory"] = os.path.isdir(file_path)
            info["extension"] = os.path.splitext(file_path)[1].lower()

            # Calculate MD5 hash for files (not directories)
            if not info["is_directory"] and info["size"] < 100 * 1024 * 1024:  # Only for files < 100MB
                try:
                    with open(file_path, 'rb') as f:
                        info["md5_hash"] = hashlib.md5(f.read()).hexdigest()
                except:
                    info["md5_hash"] = "Error calculating hash"
    except Exception as e:
        info["error"] = str(e)

    return info

def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format.

    Args:
        size_bytes: Size in bytes

    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"

def read_paths_from_csv(csv_path: str) -> List[Dict]:
    """
    Extract paths and additional information from the CSV file.

    Args:
        csv_path: Path to the CSV file

    Returns:
        List of dictionaries containing file information from the CSV
    """
    file_entries = []
    try:
        with open(csv_path, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            if "True Path" not in reader.fieldnames:
                print(f"Error: CSV file does not contain 'True Path' column. Available columns: {reader.fieldnames}")
                sys.exit(1)

            for row_index, row in enumerate(reader):
                path = row.get("True Path", "").strip()
                if path:
                    entry = {
                        "index": row_index + 1,
                        "path": path,
                        "name": row.get("Name", os.path.basename(path)),
                        "tag": row.get("Tag", ""),
                        "file_ext": row.get("File Ext", os.path.splitext(path)[1].replace(".", "")),
                        "description": row.get("Description", ""),
                        "md5": row.get("MD5", ""),
                        "original_row": row
                    }
                    file_entries.append(entry)
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        sys.exit(1)

    return file_entries

def copy_files_with_structure(source_dir: str, output_dir: str, file_entries: List[Dict], verbose: bool = False) -> List[Dict]:
    """
    Copy files from source directory to output directory maintaining folder structure.

    Args:
        source_dir: Source directory containing the files
        output_dir: Output directory where files will be copied
        file_entries: List of file entry dictionaries from CSV
        verbose: Enable verbose output

    Returns:
        List of dictionaries with detailed information about each processed file
    """
    log_entries = []
    total_files = len(file_entries)

    print(f"\nProcessing {total_files} files...")

    for entry_index, entry in enumerate(file_entries):
        start_time = time.time()
        file_path = entry["path"]

        try:
            # Normalize path separators
            normalized_path = file_path.replace('\\', os.sep).replace('/', os.sep)

            # Create source and destination paths
            source_file = os.path.join(source_dir, normalized_path)
            dest_file = os.path.join(output_dir, normalized_path)
            dest_dir = os.path.dirname(dest_file)

            # Get source file information
            source_info = get_file_info(source_file)

            # Create comprehensive log entry
            log_entry = {
                "Index": entry["index"],
                "CSV_Row": entry_index + 1,
                "File_Name": entry["name"],
                "Original_Path": file_path,
                "Source_File": source_file,
                "Destination_File": dest_file,
                "File_Extension": entry["file_ext"],
                "Tag": entry["tag"],
                "Description": entry["description"],
                "Source_Exists": source_info["exists"],
                "Source_Size": source_info["size"],
                "Source_Size_Formatted": source_info["size_formatted"],
                "Source_Modified": source_info["modified_time"],
                "Source_Created": source_info["created_time"],
                "Source_MD5": source_info.get("md5_hash", ""),
                "CSV_MD5": entry["md5"],
                "MD5_Match": "",
                "Status": "",
                "Error": "",
                "Processing_Time": "",
                "Timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # Check MD5 match if both are available
            if log_entry["Source_MD5"] and log_entry["CSV_MD5"]:
                log_entry["MD5_Match"] = "Yes" if log_entry["Source_MD5"].lower() == log_entry["CSV_MD5"].lower() else "No"

            # Progress indicator
            if verbose or (entry_index + 1) % 100 == 0:
                print(f"Processing {entry_index + 1}/{total_files}: {entry['name']}")

            # Check if source file exists
            if not source_info["exists"]:
                log_entry["Status"] = "Failed"
                log_entry["Error"] = "Source file not found"
                log_entries.append(log_entry)
                if verbose:
                    print(f"  ❌ Source file not found: {source_file}")
                continue

            # Create destination directory if it doesn't exist
            os.makedirs(dest_dir, exist_ok=True)

            # Copy the file
            shutil.copy2(source_file, dest_file)

            # Get destination file info to verify copy
            dest_info = get_file_info(dest_file)

            # Update log entry with success information
            log_entry["Status"] = "Success"
            log_entry["Processing_Time"] = f"{time.time() - start_time:.2f}s"

            # Verify file was copied correctly
            if dest_info["exists"] and dest_info["size"] == source_info["size"]:
                log_entry["Status"] = "Success"
                if verbose:
                    print(f"  ✅ Copied: {entry['name']} ({source_info['size_formatted']})")
            else:
                log_entry["Status"] = "Failed"
                log_entry["Error"] = "Copy verification failed - size mismatch"

            log_entries.append(log_entry)

        except Exception as e:
            log_entry = {
                "Index": entry.get("index", entry_index + 1),
                "CSV_Row": entry_index + 1,
                "File_Name": entry.get("name", "Unknown"),
                "Original_Path": file_path,
                "Source_File": source_file if 'source_file' in locals() else "",
                "Destination_File": dest_file if 'dest_file' in locals() else "",
                "File_Extension": entry.get("file_ext", ""),
                "Tag": entry.get("tag", ""),
                "Description": entry.get("description", ""),
                "Source_Exists": False,
                "Source_Size": 0,
                "Source_Size_Formatted": "0 B",
                "Source_Modified": "",
                "Source_Created": "",
                "Source_MD5": "",
                "CSV_MD5": entry.get("md5", ""),
                "MD5_Match": "",
                "Status": "Failed",
                "Error": str(e),
                "Processing_Time": f"{time.time() - start_time:.2f}s",
                "Timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            log_entries.append(log_entry)
            if verbose:
                print(f"  ❌ Error copying {entry.get('name', 'Unknown')}: {e}")

    return log_entries

def create_csv_log(log_entries: List[Dict], output_dir: str):
    """
    Create a comprehensive CSV log file with details of the copy operation.

    Args:
        log_entries: List of dictionaries with information about each processed file
        output_dir: Output directory where the log will be saved
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(output_dir, f"File_Processing_Log_{timestamp}.csv")

    # Define columns for the log file in the desired order
    fieldnames = [
        "Index", "CSV_Row", "File_Name", "Original_Path", "Source_File", "Destination_File",
        "File_Extension", "Tag", "Description", "Source_Exists", "Source_Size",
        "Source_Size_Formatted", "Source_Modified", "Source_Created",
        "Source_MD5", "CSV_MD5", "MD5_Match", "Status", "Error",
        "Processing_Time", "Timestamp"
    ]

    try:
        with open(log_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(log_entries)

        print(f"\nDetailed log file created: {log_file}")
        return log_file
    except Exception as e:
        print(f"Error creating log file: {e}")
        return None

def generate_summary_report(log_entries: List[Dict], output_dir: str, start_time: float, csv_path: str, source_dir: str):
    """
    Generate a summary report of the file processing operation.

    Args:
        log_entries: List of dictionaries with information about each processed file
        output_dir: Output directory where files were copied
        start_time: Start time of the operation
        csv_path: Path to the CSV file
        source_dir: Source directory
    """
    # Calculate statistics
    total_files = len(log_entries)
    success_count = sum(1 for entry in log_entries if entry["Status"] == "Success")
    failed_count = sum(1 for entry in log_entries if entry["Status"] == "Failed")
    success_rate = (success_count / total_files) * 100 if total_files > 0 else 0

    # Calculate total size
    total_size = sum(entry["Source_Size"] for entry in log_entries if entry["Status"] == "Success")
    total_size_formatted = format_file_size(total_size)

    # Calculate processing time
    total_time = time.time() - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    time_formatted = f"{int(hours):02}:{int(minutes):02}:{int(seconds):02}"

    # Generate summary report
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = os.path.join(output_dir, f"Processing_Summary_{timestamp}.txt")

    try:
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write(f"FILE PROCESSING SUMMARY\n")
            f.write("=" * 80 + "\n\n")

            f.write(f"Date and Time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"CSV File: {csv_path}\n")
            f.write(f"Source Directory: {source_dir}\n")
            f.write(f"Output Directory: {output_dir}\n\n")

            f.write(f"Total Files Processed: {total_files}\n")
            f.write(f"Successfully Copied: {success_count} ({success_rate:.1f}%)\n")
            f.write(f"Failed: {failed_count} ({100-success_rate:.1f}%)\n")
            f.write(f"Total Data Copied: {total_size_formatted}\n")
            f.write(f"Total Processing Time: {time_formatted}\n\n")

            # Add error summary if there are failures
            if failed_count > 0:
                error_types = {}
                for entry in log_entries:
                    if entry["Status"] == "Failed":
                        error = entry["Error"]
                        error_types[error] = error_types.get(error, 0) + 1

                f.write("ERROR SUMMARY:\n")
                f.write("-" * 80 + "\n")
                for error, count in error_types.items():
                    f.write(f"- {error}: {count} files\n")

            f.write("\n" + "=" * 80 + "\n")

        print(f"\nSummary report created: {summary_file}")
        return summary_file
    except Exception as e:
        print(f"Error creating summary report: {e}")
        return None

def main():
    """Main function to execute the enhanced file copy script."""
    start_time = time.time()

    # Parse command line arguments
    args = parse_arguments()

    source_dir = args.source
    csv_path = args.csv
    output_dir = args.output if args.output else os.path.join(os.getcwd(), "Output")
    verbose = args.verbose

    # Display script header
    print("=" * 80)
    print("ENHANCED FILE COPY SCRIPT")
    print("=" * 80)
    print(f"Started at: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Validate inputs
    if not os.path.exists(source_dir):
        print(f"❌ Error: Source directory not found: {source_dir}")
        sys.exit(1)

    if not os.path.exists(csv_path):
        print(f"❌ Error: CSV file not found: {csv_path}")
        sys.exit(1)

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    print(f"\n📁 Source directory: {source_dir}")
    print(f"📄 CSV file: {csv_path}")
    print(f"📂 Output directory: {output_dir}")
    print(f"🔍 Verbose mode: {'Enabled' if verbose else 'Disabled'}")

    # Read file entries from CSV
    print("\n📖 Reading file entries from CSV...")
    file_entries = read_paths_from_csv(csv_path)
    print(f"✅ Found {len(file_entries)} file entries in CSV")

    # Copy files with enhanced logging
    print("\n🚀 Starting file copy operation...")
    log_entries = copy_files_with_structure(source_dir, output_dir, file_entries, verbose)

    # Create detailed log
    print("\n📝 Creating detailed log file...")
    log_file = create_csv_log(log_entries, output_dir)

    # Generate summary report
    print("📊 Generating summary report...")
    summary_file = generate_summary_report(log_entries, output_dir, start_time, csv_path, source_dir)

    # Calculate and display final statistics
    success_count = sum(1 for entry in log_entries if entry["Status"] == "Success")
    failed_count = sum(1 for entry in log_entries if entry["Status"] == "Failed")
    success_rate = (success_count / len(file_entries)) * 100 if file_entries else 0

    # Calculate total size copied
    total_size = sum(entry["Source_Size"] for entry in log_entries if entry["Status"] == "Success")
    total_size_formatted = format_file_size(total_size)

    # Calculate processing time
    total_time = time.time() - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    time_formatted = f"{int(hours):02}:{int(minutes):02}:{int(seconds):02}"

    # Display final summary
    print("\n" + "=" * 80)
    print("FINAL PROCESSING SUMMARY")
    print("=" * 80)
    print(f"📊 Total files processed: {len(file_entries)}")
    print(f"✅ Successfully copied: {success_count} ({success_rate:.1f}%)")
    print(f"❌ Failed: {failed_count} ({100-success_rate:.1f}%)")
    print(f"💾 Total data copied: {total_size_formatted}")
    print(f"⏱️  Total processing time: {time_formatted}")
    print(f"📄 Log file: {log_file}")
    print(f"📋 Summary report: {summary_file}")
    print("=" * 80)

    # Exit with appropriate code
    if failed_count > 0:
        print(f"\n⚠️  Warning: {failed_count} files failed to copy. Check the log file for details.")
        sys.exit(1)
    else:
        print(f"\n🎉 All files copied successfully!")
        sys.exit(0)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Operation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        sys.exit(1)