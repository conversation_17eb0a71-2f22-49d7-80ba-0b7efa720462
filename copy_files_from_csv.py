#!/usr/bin/env python3
"""
Script to copy files based on paths in a CSV file to an Output folder.

This script:
1. Reads a CSV file with a "True Path" column
2. Creates folder structure based on paths in the CSV
3. Copies files from source directory to Output directory
4. Generates a CSV log with details of the operation
"""

import os
import csv
import shutil
import argparse
import datetime
import sys
from pathlib import Path
from typing import List, Dict, Tuple

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Copy files listed in a CSV to an Output folder with original structure",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Copy files from source directory based on CSV file paths
  python copy_files_from_csv.py -s F:\\Project_Stargazing\\Encase\\Existing_actual_files -c file_list.csv
        """
    )

    parser.add_argument('-s', '--source', required=True,
                       help='Source directory containing the files to copy')
    parser.add_argument('-c', '--csv', required=True,
                       help='Path to the CSV file with "True Path" column')
    parser.add_argument('-o', '--output',
                       help='Output directory (default: "Output" in current directory)')
    
    return parser.parse_args()

def read_paths_from_csv(csv_path: str) -> List[str]:
    """
    Extract paths from the "True Path" column in the CSV file.
    
    Args:
        csv_path: Path to the CSV file
        
    Returns:
        List of paths from the "True Path" column
    """
    paths = []
    try:
        with open(csv_path, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            if "True Path" not in reader.fieldnames:
                print(f"Error: CSV file does not contain 'True Path' column. Available columns: {reader.fieldnames}")
                sys.exit(1)
            
            for row in reader:
                path = row["True Path"]
                if path and path.strip():
                    paths.append(path.strip())
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        sys.exit(1)
        
    return paths

def copy_files_with_structure(source_dir: str, output_dir: str, paths: List[str]) -> List[Dict]:
    """
    Copy files from source directory to output directory maintaining folder structure.
    
    Args:
        source_dir: Source directory containing the files
        output_dir: Output directory where files will be copied
        paths: List of paths from CSV file
        
    Returns:
        List of dictionaries with information about each copied file
    """
    log_entries = []
    
    for path_index, file_path in enumerate(paths):
        try:
            # Normalize path separators
            normalized_path = file_path.replace('\\', os.sep).replace('/', os.sep)
            
            # Create source and destination paths
            source_file = os.path.join(source_dir, normalized_path)
            dest_file = os.path.join(output_dir, normalized_path)
            dest_dir = os.path.dirname(dest_file)
            
            # Create log entry
            log_entry = {
                "Index": path_index + 1,
                "Original Path": file_path,
                "Source File": source_file,
                "Destination File": dest_file,
                "Status": "",
                "Error": "",
                "Timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Check if source file exists
            if not os.path.exists(source_file):
                log_entry["Status"] = "Failed"
                log_entry["Error"] = "Source file not found"
                log_entries.append(log_entry)
                print(f"Error: Source file not found: {source_file}")
                continue
                
            # Create destination directory if it doesn't exist
            os.makedirs(dest_dir, exist_ok=True)
            
            # Copy the file
            shutil.copy2(source_file, dest_file)
            log_entry["Status"] = "Success"
            log_entries.append(log_entry)
            print(f"Copied: {source_file} -> {dest_file}")
            
        except Exception as e:
            log_entry = {
                "Index": path_index + 1,
                "Original Path": file_path,
                "Source File": source_file if 'source_file' in locals() else "",
                "Destination File": dest_file if 'dest_file' in locals() else "",
                "Status": "Failed",
                "Error": str(e),
                "Timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            log_entries.append(log_entry)
            print(f"Error copying file {file_path}: {e}")
    
    return log_entries

def create_csv_log(log_entries: List[Dict], output_dir: str):
    """
    Create a CSV log file with details of the copy operation.
    
    Args:
        log_entries: List of dictionaries with information about each copied file
        output_dir: Output directory where the log will be saved
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(output_dir, f"File_Copy_Log_{timestamp}.csv")
    
    fieldnames = ["Index", "Original Path", "Source File", "Destination File", "Status", "Error", "Timestamp"]
    
    try:
        with open(log_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(log_entries)
        
        print(f"\nLog file created: {log_file}")
    except Exception as e:
        print(f"Error creating log file: {e}")

def main():
    """Main function to execute the script."""
    args = parse_arguments()
    
    source_dir = args.source
    csv_path = args.csv
    output_dir = args.output if args.output else os.path.join(os.getcwd(), "Output")
    
    # Validate inputs
    if not os.path.exists(source_dir):
        print(f"Error: Source directory not found: {source_dir}")
        sys.exit(1)
        
    if not os.path.exists(csv_path):
        print(f"Error: CSV file not found: {csv_path}")
        sys.exit(1)
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"Source directory: {source_dir}")
    print(f"CSV file: {csv_path}")
    print(f"Output directory: {output_dir}")
    
    # Read paths from CSV
    print("\nReading paths from CSV file...")
    paths = read_paths_from_csv(csv_path)
    print(f"Found {len(paths)} paths in CSV file")
    
    # Copy files
    print("\nCopying files...")
    log_entries = copy_files_with_structure(source_dir, output_dir, paths)
    
    # Create log
    print("\nCreating log file...")
    create_csv_log(log_entries, output_dir)
    
    # Print summary
    success_count = sum(1 for entry in log_entries if entry["Status"] == "Success")
    failed_count = sum(1 for entry in log_entries if entry["Status"] == "Failed")
    
    print("\n" + "=" * 50)
    print("COPY SUMMARY")
    print("=" * 50)
    print(f"Total paths: {len(paths)}")
    print(f"Successfully copied: {success_count}")
    print(f"Failed: {failed_count}")
    print("=" * 50)

if __name__ == "__main__":
    main()