#!/usr/bin/env python3
"""
Utility script to restore files back to Attachments folders.

This script moves all files from the organized folder structure back to their
respective Attachments folders in case you need to revert the organization.
"""

import os
import shutil

def restore_files_to_attachments(export_dir: str) -> None:
    """
    Restore all files from organized folders back to Attachments.
    
    Args:
        export_dir: Path to the export directory
    """
    attachments_dir = os.path.join(export_dir, 'Attachments')
    
    if not os.path.exists(attachments_dir):
        os.makedirs(attachments_dir)
        print(f"Created Attachments directory: {attachments_dir}")
    
    files_moved = 0
    
    # Walk through all subdirectories except Attachments
    for root, dirs, files in os.walk(export_dir):
        # Skip the Attachments directory itself
        if 'Attachments' in root:
            continue
            
        # Skip the root export directory
        if root == export_dir:
            continue
        
        # Move all files found in subdirectories
        for file in files:
            if file.endswith(('.xlsx', '.xls', '.csv', '.doc', '.docx', '.pdf', '.txt')):
                source_file = os.path.join(root, file)
                dest_file = os.path.join(attachments_dir, file)
                
                # Handle duplicate filenames
                counter = 1
                original_dest = dest_file
                while os.path.exists(dest_file):
                    name, ext = os.path.splitext(original_dest)
                    dest_file = f"{name} ({counter}){ext}"
                    counter += 1
                
                try:
                    shutil.move(source_file, dest_file)
                    print(f"Restored: {file} -> Attachments/")
                    files_moved += 1
                except Exception as e:
                    print(f"Error moving {file}: {e}")
    
    print(f"Total files restored to {export_dir}/Attachments: {files_moved}")
    
    # Clean up empty directories
    cleanup_empty_dirs(export_dir)

def cleanup_empty_dirs(export_dir: str) -> None:
    """
    Remove empty directories after moving files.
    
    Args:
        export_dir: Path to the export directory
    """
    dirs_removed = 0
    
    # Walk bottom-up to remove empty directories
    for root, dirs, files in os.walk(export_dir, topdown=False):
        # Skip the Attachments directory and root directory
        if 'Attachments' in root or root == export_dir:
            continue
        
        # Remove directory if it's empty
        try:
            if not os.listdir(root):  # Directory is empty
                os.rmdir(root)
                print(f"Removed empty directory: {root}")
                dirs_removed += 1
        except Exception as e:
            print(f"Error removing directory {root}: {e}")
    
    print(f"Total empty directories removed: {dirs_removed}")

def main():
    """Main function to restore files in both Export folders."""
    print("Starting File Restoration Script")
    print("=" * 50)
    print("This will move all organized files back to Attachments folders")
    
    # Ask for confirmation
    response = input("Are you sure you want to proceed? (y/N): ").strip().lower()
    if response != 'y':
        print("Operation cancelled.")
        return
    
    # Define export folders
    export_folders = ['Export', 'Export (1)']
    
    for folder in export_folders:
        if os.path.exists(folder):
            print(f"\nProcessing: {folder}")
            restore_files_to_attachments(folder)
        else:
            print(f"Warning: Folder '{folder}' not found")
    
    print("\n" + "=" * 50)
    print("Restoration completed!")
    print("All files have been moved back to their respective Attachments folders.")

if __name__ == "__main__":
    main()
