#!/usr/bin/env python3
"""
<PERSON>ript to check if all required dependencies are installed for the enhanced file processing script.
"""

import sys

def check_requirements():
    """Check if all required modules are available."""
    print("Checking requirements for Enhanced Export File Processing Script")
    print("=" * 60)
    
    # Standard library modules
    standard_modules = [
        'os', 'csv', 'shutil', 're', 'datetime', 'dataclasses', 'typing'
    ]
    
    print("\n✓ Checking standard library modules:")
    for module in standard_modules:
        try:
            __import__(module)
            print(f"  ✓ {module}")
        except ImportError:
            print(f"  ✗ {module} - MISSING")
            return False
    
    # Optional Excel logging module
    print("\n✓ Checking Excel logging requirements:")
    try:
        import openpyxl
        print(f"  ✓ openpyxl (version: {openpyxl.__version__})")
        excel_available = True
    except ImportError:
        print("  ⚠ openpyxl - NOT INSTALLED")
        print("    Excel logging will be disabled")
        print("    Install with: pip install openpyxl")
        excel_available = False
    
    print("\n" + "=" * 60)
    if excel_available:
        print("✓ All requirements satisfied! Excel logging will be available.")
    else:
        print("⚠ Basic functionality available, but Excel logging is disabled.")
        print("  Install openpyxl for full functionality.")
    
    return True

if __name__ == "__main__":
    check_requirements()
