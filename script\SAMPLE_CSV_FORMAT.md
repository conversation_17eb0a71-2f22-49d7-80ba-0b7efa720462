# Sample CSV Format for Export File Processing

This document shows the expected CSV format for the enhanced export file processing script.

## Required CSV Columns

The CSV files in your Export folders must contain these required columns:

- **Carved Filename**: The carved filename in the Attachments folder (e.g., `0104731_Carved.png`) - **Can be empty**
- **Filename**: The original filename (e.g., `Testing_123.png`)
- **Source**: The original full path where the file was found
- **Artifacts type**: The type of artifact (optional, e.g., `Images`, `Documents`, `Archives`)

## Sample CSV Structure

```csv
Carved Filename,Filename,Source,Artifacts type,Size,Modified,Created
0104731_Carved.png,Testing_123.png,TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) WinRE_DRV\Recovery\WindowsRE\winre$#*.wim,Images,15234,2024-01-15 10:30:22,2024-01-15 09:45:10
0104732_Carved.docx,Report*Document?.docx,TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) Windows\Users\subha\Documents\report<file>.docx,Documents,28456,2024-01-16 14:22:33,2024-01-16 14:20:15
,Presentation|File.pdf,TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) Windows\Users\subha\Desktop\presentation"file".pdf,Documents,1245678,2024-01-17 16:45:12,2024-01-17 16:40:30
0104734_Carved.zip,Archive_File.zip,TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) Windows\Users\subha\Downloads\archive.zip,Archives,2048576,2024-01-18 09:15:45,2024-01-18 09:10:20
```

## How the Script Processes This Data

### 1. Carved Filename Extraction
- From `Carved Filename` column: `0104731_Carved.png`
- This is the actual file in the Attachments folder

### 2. Origin Filename Extraction
- From `Filename` column: `Testing_123.png`
- Special characters are sanitized: `Report*Document?.docx` → `Report_Document_.docx`
- This becomes the final renamed filename

### 3. Path Transformation and Sanitization
- Original: `TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) WinRE_DRV\Recovery\WindowsRE\winre$#*.wim`
- Cleaned: `C:\WinRE_DRV\Recovery\WindowsRE\winre___.wim`
- Prohibited characters (`\ / : * ? " < > |`) are replaced with underscores
- Folder created: `Export\WinRE_DRV\Recovery\WindowsRE\`

### 4. File Processing Result
- Source file: `Export\Attachments\0104731_Carved.png`
- Destination: `Export\WinRE_DRV\Recovery\WindowsRE\Testing_123.png`
- Action: Move and rename from carved filename to sanitized original filename

### 5. Empty Carved Filename Handling
- **Empty Entry**: Third row has empty `Carved Filename`
- **Processing**: Entry is logged but no file movement occurs
- **Log Status**: "Skipped - Empty carved filename"
- **Purpose**: Tracks entries without actual carved files

### 6. Special Character Handling
The script automatically handles prohibited Windows filename characters:
- `Report*Document?.docx` → `Report_Document_.docx`
- `presentation"file".pdf` → `presentation_file_.pdf`
- `winre$#*.wim` → `winre___.wim`

## Excel Log Output

The script will log each file with these details:

| Carved Filename | Origin Filename | Replaced Filename | Origin Folder Path | Replaced Folder Path | Error Status | Artifacts Type |
|----------------|----------------|-------------------|-------------------|---------------------|--------------|----------------|
| 0104731_Carved.png | Testing_123.png | Testing_123.png | TESTING_LT01_HDD01.E01 - Partition 4... | Export\WinRE_DRV\Recovery\WindowsRE | No error | Images |
| 0104732_Carved.docx | Report*Document?.docx | Report_Document_.docx | TESTING_LT01_HDD01.E01 - Partition 4... | Export\Windows\Users\subha\Documents | No error | Documents |
| | Presentation\|File.pdf | | TESTING_LT01_HDD01.E01 - Partition 4... | | Skipped - Empty carved filename | Documents |

## Notes

- Additional columns in the CSV (like Size, Modified, Created) are ignored
- The script requires `Carved Filename`, `Filename`, and `Source` columns to function
- **Empty `Carved Filename`**: Entries with empty carved filenames are logged but skipped from file processing
- **`Artifacts type`**: Optional column that will be included in Excel logs if present
- File extensions are preserved during the renaming process
- Prohibited characters in filenames and paths are automatically replaced with underscores
- Duplicate filenames in the same destination folder are handled automatically with numbering
- Original folder paths in the log show the raw source data, while replaced folder paths show the sanitized destination
