#!/usr/bin/env python3
"""
Debug script to examine Excel log entries and identify "Source file not found" issues.
"""

import pandas as pd
import os
import sys

def debug_excel_log(excel_file_path):
    """Debug the Excel log to understand Source file not found errors."""

    if not os.path.exists(excel_file_path):
        print(f"❌ Excel file not found: {excel_file_path}")
        return

    try:
        # Read the Excel file
        df = pd.read_excel(excel_file_path)
        print(f"📊 Total records in Excel log: {len(df)}")
        print(f"📋 Columns: {list(df.columns)}")

        # Get list of actual files in Organized_Files
        organized_files_dir = "Organized_Files"
        actual_files = set()
        if os.path.exists(organized_files_dir):
            for item in os.listdir(organized_files_dir):
                if os.path.isfile(os.path.join(organized_files_dir, item)):
                    actual_files.add(item)

        print(f"📁 Files actually in Organized_Files: {len(actual_files)}")

        # Filter for "Source file not found" errors
        error_mask = df['Error Status'].str.contains('Source file not found', na=False)
        error_entries = df[error_mask]

        print(f"\n🔍 Found {len(error_entries)} 'Source file not found' errors")

        # Check CSV data
        csv_file = "Compiled_all.csv"
        if os.path.exists(csv_file):
            csv_df = pd.read_csv(csv_file)
            csv_carved_files = set(csv_df['Carved Filename'].dropna())
            print(f"📄 Carved files in CSV: {len(csv_carved_files)}")

            # Find files that exist in CSV but not in Organized_Files
            missing_files = csv_carved_files - actual_files
            print(f"❌ Files in CSV but missing from Organized_Files: {len(missing_files)}")

            # Find files that exist in Organized_Files but not in CSV
            extra_files = actual_files - csv_carved_files
            print(f"➕ Files in Organized_Files but not in CSV: {len(extra_files)}")

            if len(missing_files) > 0:
                print(f"\n📝 Sample missing files (first 10):")
                for i, filename in enumerate(sorted(missing_files)[:10]):
                    print(f"  {i+1}. {filename}")

        if len(error_entries) > 0:
            print("\n📝 Sample error entries:")
            for idx, row in error_entries.head(5).iterrows():
                carved_filename = row['Carved Filename']
                error_status = row['Error Status']
                origin_folder = row['Origin Folder Path']

                print(f"  Row {idx + 2}: {carved_filename}")
                print(f"    Error: {error_status}")
                print(f"    Origin: {origin_folder}")

                # Check if the file actually exists in Organized_Files
                file_exists = carved_filename in actual_files
                print(f"    File exists in Organized_Files: {file_exists}")

                if file_exists:
                    print(f"    ⚠️  FILE EXISTS BUT LOGGED AS NOT FOUND!")
                print()

        # Check for successful entries
        success_mask = df['Error Status'] == 'No error'
        success_entries = df[success_mask]
        print(f"✅ Successful entries: {len(success_entries)}")

        # Check for other error types
        other_errors = df[~error_mask & (df['Error Status'] != 'No error') & (df['Error Status'] != 'Skipped - Empty carved filename')]
        print(f"⚠️  Other errors: {len(other_errors)}")

        if len(other_errors) > 0:
            print("\n📝 Other error types:")
            error_types = other_errors['Error Status'].value_counts()
            for error_type, count in error_types.head(5).items():
                print(f"  {error_type}: {count}")

    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")

if __name__ == "__main__":
    excel_file = "File_Processing_Log_Compiled_all_20250711_193123.xlsx"
    debug_excel_log(excel_file)
